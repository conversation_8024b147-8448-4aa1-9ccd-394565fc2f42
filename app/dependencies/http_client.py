import logging
from typing import Annotated, AsyncGenerator

from backoff import expo, on_exception, types
from fastapi import Depends, status
import httpx

from config import settings


__all__ = ['CustomAsyncClient', 'HTTPClientDep']


logger = logging.getLogger(__name__)


def _log_giveup(details: types.Details) -> None:
    """Log giveups."""
    target = details['target'].__qualname__
    args = details['args'][1:]
    error = details['exception']  # pyright: ignore[reportGeneralTypeIssues]
    error_type = type(error)
    logger.warning(
        'Giving up %s%s for the error "%s.%s: %s"', target, args, error_type.__module__, error_type.__name__, error
    )


def _should_givup(exception: Exception) -> bool:
    """
    Determine if we should give up for a given exception.

    Args:
        exception: The exception to check

    Returns:
        bool: True if we should retry, False otherwise
    """
    if isinstance(exception, httpx.HTTPStatusError):
        # Retry on specific HTTP status codes
        return exception.response.status_code not in {
            status.HTTP_408_REQUEST_TIMEOUT,
            status.HTTP_429_TOO_MANY_REQUESTS,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_502_BAD_GATEWAY,
            status.HTTP_503_SERVICE_UNAVAILABLE,
            status.HTTP_504_GATEWAY_TIMEOUT,
        }

    # Retry on request errors (connection issues, etc.)
    return not isinstance(exception, httpx.RequestError)


http_client_backoff = on_exception(
    expo,
    (httpx.HTTPStatusError, httpx.RequestError),
    max_tries=settings.http_client.backoff.max_retries,
    giveup=_should_givup,
    on_giveup=_log_giveup,
    logger=None,
    base=settings.http_client.backoff.backoff_base,
    factor=settings.http_client.backoff.backoff_factor,
)


class CustomAsyncClient(httpx.AsyncClient):
    def __init__(self, *args, raise_for_status: bool = True, **kwargs):
        super().__init__(*args, **kwargs)
        self._raise_for_status = raise_for_status

    @http_client_backoff
    async def request(self, *args, **kwargs) -> httpx.Response:
        response = await super().request(*args, **kwargs)
        if self._raise_for_status:
            if response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
                response.status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            response.raise_for_status()
        return response


async def get_http_client() -> AsyncGenerator[CustomAsyncClient, None]:
    """
    Dependency that provides a new HTTP client for each request.
    Each request gets its own client that is properly cleaned up after use.
    """
    async with CustomAsyncClient(
        timeout=httpx.Timeout(timeout=settings.http_client.timeout),
        follow_redirects=settings.http_client.follow_redirects,
        verify=settings.http_client.verify_ssl,
        limits=httpx.Limits(
            max_connections=settings.http_client.max_connections,
            max_keepalive_connections=settings.http_client.max_keepalive_connections,
        ),
    ) as client:
        yield client


HTTPClientDep = Annotated[CustomAsyncClient, Depends(get_http_client)]
