from functools import cached_property
import logging
import mimetypes
import os
from typing import Sequence, cast

from dotenv import find_dotenv, load_dotenv

from constants.environment import Environment
from constants.operation_ids import operation_ids
from core.schemas import CustomModel


__all__ = ['settings']


if env_file := os.getenv('ENV_FILE'):
    load_dotenv(dotenv_path=find_dotenv(filename=env_file), override=True)


TRUE_SYNONYMS = {'true', 'yes', '1'}

ENVIRONMENT = Environment(os.environ['ENVIRONMENT'])
QUALS_CLIENT_API_MOCKED_ENVS = (Environment.LOCAL, Environment.TEST)


class ADSettings(CustomModel):
    aad_instance: str = 'https://login.microsoftonline.com'
    api_audience: str
    aad_tenant_id: str
    required_scopes: frozenset


class SignalRSettings(CustomModel):
    connection_string: str
    token_lifespan: int = 60  # minutes
    hub_name: str = 'qualMessageHub'


class AuthSettings(CustomModel):
    ad: ADSettings
    signal_r: SignalRSettings
    auth_free_endpoints: frozenset[str] = frozenset(
        (operation_ids.root.HEALTH_CHECK, operation_ids.conversation.GET_EXTRA_DATA)
    )


DEFAULT_CONTENT_QUEUE_NAME = 'content-analysis-queue'


class DatabaseSettings(CustomModel):
    host: str
    port: str
    user: str
    password: str
    name: str
    driver: str

    @cached_property
    def uri(self) -> str:
        return (
            f'mssql+aioodbc:///?odbc_connect=DRIVER={{{self.driver}}};SERVER={self.host},{self.port};'
            f'DATABASE={self.name};UID={self.user};PWD={self.password};MARS_Connection=Yes;'
        )


class BlobStorageSettings(CustomModel):
    """Settings for Azure Blob Storage."""

    connection_string: str
    container_name: str
    max_file_size: int = 250 * 1024 * 1024 + (1024 * 10)  # 250 MB + 10 KB buffer
    max_docs_per_conversation: int = 3
    max_conversation_size: int = 250 * 1024 * 1024 + (1024 * 10)  # 250 MB + 10 KB buffer
    supported_file_formats: set[str] = {
        # Word documents
        'application/msword',  # .doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # .docx
        # PDF documents
        'application/pdf',  # .pdf
        # PowerPoint presentations
        'application/vnd.ms-powerpoint',  # .ppt
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',  # .pptx
    }

    @cached_property
    def supported_extensions(self) -> set[str]:
        return {ext[1:] for mime_type in self.supported_file_formats if (ext := mimetypes.guess_extension(mime_type))}


class KXDashAPISettings(CustomModel):
    base_url: str


class QualsClientsAPISettings(CustomModel):
    base_url: str
    # mock_client_api_enabled: bool = ENVIRONMENT in QUALS_CLIENT_API_MOCKED_ENVS
    mock_client_api_enabled: bool = ENVIRONMENT in QUALS_CLIENT_API_MOCKED_ENVS



class IndustriesAPISettings(CustomModel):
    base_url: str


class ServicesAPISettings(CustomModel):
    base_url: str


class RolesAPISettings(CustomModel):
    base_url: str


class LDMFCountriesAPISettings(CustomModel):
    base_url: str


class HTTPClientBackoffSettings(CustomModel):
    max_retries: int = 3
    backoff_base: int = 60
    backoff_factor: float = 0.5


class HTTPClientSettings(CustomModel):
    timeout: int  # seconds
    follow_redirects: bool
    verify_ssl: bool
    max_connections: int
    max_keepalive_connections: int
    backoff: HTTPClientBackoffSettings


class QueueSettings(CustomModel):
    connection_string: str
    content_queue_name: str


class OpenAISettings(CustomModel):
    """Settings for Azure OpenAI API."""

    endpoint: str
    key: str
    deployment: str
    model: str
    api_version: str
    default_temperature: float


class Settings(CustomModel):
    project_name: str = 'KX GenAI Qual'
    version: str = '1'
    environment: Environment
    log_level: int
    debug: bool
    allowed_hosts: Sequence[str]
    auth: AuthSettings
    db: DatabaseSettings
    document_storage: BlobStorageSettings
    http_client: HTTPClientSettings
    kx_dash_api: KXDashAPISettings
    quals_clients_api: QualsClientsAPISettings
    document_queue: QueueSettings
    openai: OpenAISettings
    industries_api: IndustriesAPISettings
    services_api: ServicesAPISettings
    roles_api: RolesAPISettings
    ldmf_countries_api: LDMFCountriesAPISettings

    # Debugging & logging
    append_collected_data_to_message_response: bool


settings = Settings(
    environment=ENVIRONMENT,
    log_level=cast(int, getattr(logging, os.environ['LOG_LEVEL'].upper())),
    debug=os.getenv('DEBUG') in TRUE_SYNONYMS,
    allowed_hosts=tuple(x for host in os.getenv('ALLOWED_HOSTS', '*').split(',') if (x := host.strip())),
    auth=AuthSettings(
        ad=ADSettings(
            api_audience=os.environ['AZURE_AD_API_AUDIENCE'],
            aad_tenant_id=os.environ['AZURE_AD_TENANT_ID'],
            required_scopes=frozenset(os.environ['AZURE_AD_REQUIRED_SCOPES'].strip().split(',')),
        ),
        signal_r=SignalRSettings(
            connection_string=os.environ['SIGNAL_R_CONNECTION_STRING'],
        ),
    ),
    db=DatabaseSettings(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        user=os.environ['DB_USER'],
        password=os.environ['DB_PASSWORD'],
        name=('test_' if ENVIRONMENT == Environment.TEST else '') + os.environ['DB_NAME'],
        driver=os.environ.get('DB_DRIVER', 'ODBC+Driver+17+for+SQL+Server'),
    ),
    document_storage=BlobStorageSettings(
        connection_string=os.environ['AZURE_STORAGE_CONNECTION_STRING'],
        container_name=os.getenv('AZURE_STORAGE_CONTAINER_NAME', 'documents'),
    ),
    http_client=HTTPClientSettings(
        timeout=int(os.environ['HTTP_CLIENT_TIMEOUT']),
        follow_redirects=os.environ['HTTP_CLIENT_FOLLOW_REDIRECTS'].lower() in TRUE_SYNONYMS,
        verify_ssl=os.environ['HTTP_CLIENT_VERIFY_SSL'].lower() in TRUE_SYNONYMS,
        max_connections=int(os.environ['HTTP_CLIENT_MAX_CONNECTIONS']),
        max_keepalive_connections=int(os.environ['HTTP_CLIENT_MAX_KEEPALIVE_CONNECTIONS']),
        backoff=HTTPClientBackoffSettings(),
    ),
    kx_dash_api=KXDashAPISettings(
        base_url=os.environ['KX_DASH_API_BASE_URL'],
    ),
    quals_clients_api=QualsClientsAPISettings(
        base_url=os.environ['QUALS_API_BASE_URL'],
    ),
    document_queue=QueueSettings(
        connection_string=os.environ['AZURE_QUEUE_CONNECTION_STRING'],
        content_queue_name=os.getenv('AZURE_CONTENT_QUEUE_NAME', DEFAULT_CONTENT_QUEUE_NAME)
        + ('-test' if ENVIRONMENT == Environment.TEST else ''),
    ),
    openai=OpenAISettings(
        endpoint=os.environ['AZURE_OPENAI_ENDPOINT'],
        key=os.environ['AZURE_OPENAI_KEY'],
        deployment=os.getenv('AZURE_OPENAI_DEPLOYMENT', 'gpt-4o'),
        model=os.getenv('AZURE_OPENAI_MODEL', 'gpt-4o'),
        api_version=os.getenv('AZURE_OPENAI_API_VERSION', '2024-08-01-preview'),
        default_temperature=float(os.getenv('AZURE_OPENAI_DEFAULT_TEMPERATURE', 0.0)),
    ),
    industries_api=IndustriesAPISettings(
        base_url=os.environ['INDUSTRIES_API_BASE_URL'],
    ),
    services_api=ServicesAPISettings(
        base_url=os.environ['SERVICES_API_BASE_URL'],
    ),
    roles_api=RolesAPISettings(
        base_url=os.environ['ROLES_API_BASE_URL'],
    ),
    ldmf_countries_api=LDMFCountriesAPISettings(
        base_url=os.environ['LDMF_COUNTRIES_BASE_URL'],
    ),
    append_collected_data_to_message_response=os.environ.get(
        'APPEND_COLLECTED_DATA_TO_MESSAGE_RESPONSE', 'true'
    ).lower()
    in TRUE_SYNONYMS,
)
